<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Flight Step Progress</title>
    <style>
      body {
        font-family: sans-serif;
        background: #f9f9f9;
        padding: 30px;
      }
      .progress-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: white;
        border: 1px solid #ddd;
        border-radius: 6px;
        overflow: hidden;
      }
      .route-info {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 12px 20px;
        gap: 10px;
        flex-wrap: wrap;
      }
      .step-container {
        flex: 1;
        display: flex;
      }
      .tag {
        background: #f8f1e5;
        color: #4a3f24;
        border-radius: 20px;
        padding: 3px 10px;
        font-size: 14px;
      }
      .text {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }
      .edit-search {
        margin-left: auto;
        margin-right: 10px;
        color: #7d6650;
        cursor: pointer;
        font-size: 14px;
      }
      .edit-search::after {
        content: '▶';
        margin-left: 6px;
        font-size: 12px;
        color: #c00;
      }
      .step {
        display: flex;
        align-items: center;
        background: #fdf7ee;
        padding: 10px 16px;
        border-left: 1px solid #eee;
      }
      .step.active {
        background: #c00;
        color: white;
        font-weight: bold;
      }
      .step-circle {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #ccc;
        color: white;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        margin-right: 6px;
      }
      .step.active .step-circle {
        background: white;
        color: #c00;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="progress-bar">
      <div class="route-info">
        <div>
          <div class="tag">One way</div>
          <div class="text">Beijing → Guangzhou</div>
        </div>

        <div>
          <div class="tag">Departure:2025-06-14</div>
          <div class="text">Adult×1, Child×0, Infant×0</div>
        </div>

        <div class="edit-search">Edit search</div>
      </div>

      <div class="step-container">
        <div class="step active">
          <div class="step-circle">1</div>
          Select
        </div>
        <div class="step">
          <div class="step-circle">3</div>
          Review
        </div>
        <div class="step">
          <div class="step-circle">3</div>
          Information
        </div>
        <div class="step">
          <div class="step-circle">4</div>
          Self-service
        </div>
        <div class="step">
          <div class="step-circle">5</div>
          Pay
        </div>
        <div class="step">
          <div class="step-circle">6</div>
          Completed
        </div>
      </div>
    </div>
  </body>
</html>
